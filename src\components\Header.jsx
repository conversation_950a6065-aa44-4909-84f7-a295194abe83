import { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'

const Header = ({ onSearch, searchQuery, setSearchQuery }) => {
  const location = useLocation()
  
  const isActive = (path) => {
    if (path === '/' && location.pathname === '/') return true
    if (path !== '/' && location.pathname.startsWith(path)) return true
    return false
  }

  const handleSearchChange = (e) => {
    const query = e.target.value
    setSearchQuery(query)
    if (onSearch) {
      onSearch(query)
    }
  }

  const getSearchPlaceholder = () => {
    if (location.pathname.startsWith('/authors')) return 'Search Authors...'
    return 'Search...'
  }

  return (
    <header className="header" role="banner">
      <Link to="/" className="logo" aria-label="SAYARI - Go to homepage">
        SAYARI
      </Link>

      <nav className="nav" role="navigation" aria-label="Main navigation">
        <Link
          to="/"
          className={`nav-item ${isActive('/') ? 'active' : ''}`}
          aria-current={isActive('/') ? 'page' : undefined}
        >
          All
        </Link>
        <Link
          to="/category/shayari"
          className={`nav-item ${isActive('/category/shayari') ? 'active' : ''}`}
          aria-current={isActive('/category/shayari') ? 'page' : undefined}
        >
          Shayari
        </Link>
        <Link
          to="/category/quotes"
          className={`nav-item ${isActive('/category/quotes') ? 'active' : ''}`}
          aria-current={isActive('/category/quotes') ? 'page' : undefined}
        >
          Quotes
        </Link>
        <Link
          to="/category/wishes"
          className={`nav-item ${isActive('/category/wishes') ? 'active' : ''}`}
          aria-current={isActive('/category/wishes') ? 'page' : undefined}
        >
          Wishes
        </Link>
        <Link
          to="/authors"
          className={`nav-item ${isActive('/authors') ? 'active' : ''}`}
          aria-current={isActive('/authors') ? 'page' : undefined}
        >
          Authors
        </Link>
      </nav>

      <div className="search-container" role="search">
        <label htmlFor="search-input" className="sr-only">
          Search content
        </label>
        <input
          id="search-input"
          type="text"
          className="search"
          placeholder={getSearchPlaceholder()}
          value={searchQuery}
          onChange={handleSearchChange}
          aria-describedby="search-help"
        />
        <div id="search-help" className="sr-only">
          Type to search for posts, authors, or categories
        </div>
      </div>
    </header>
  )
}

export default Header
