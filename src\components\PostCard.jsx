import { Link } from 'react-router-dom'
import LazyImage from './LazyImage'
import OptimizedImage from './OptimizedImage'
import { getExcerpt, getThumbnailImage, formatDate } from '../utils/contentUtils'

const PostCard = ({ post, featured = false }) => {


  const getAuthorName = (authorId) => {
    // For now, return a placeholder. In a real app, you'd fetch author data
    return 'Admin'
  }

  if (featured) {
    const thumbnailUrl = getThumbnailImage(post.content)

    return (
      <article className="featured-post" role="article">
        <Link
          to={`/${post.slug}`}
          className="featured"
          aria-label={`Read featured post: ${post.title}`}
        >
          {thumbnailUrl && (
            <div className="featured-image">
              <OptimizedImage
                src={thumbnailUrl}
                alt={`Featured image for ${post.title}`}
                width={800}
                height={200}
                priority={true}
                style={{
                  borderRadius: '8px',
                  marginBottom: '15px'
                }}
              />
            </div>
          )}
          <header>
            <h2 className="featured-title">{post.title}</h2>
          </header>
          <div className="featured-content" aria-label="Post excerpt">
            {getExcerpt(post.content, 200)}
          </div>
          <footer className="featured-author">
            <span aria-label="Author">By {getAuthorName(post.author_id)}</span>
          </footer>
        </Link>
      </article>
    )
  }

  const thumbnailUrl = getThumbnailImage(post.content)

  return (
    <article className="poem-card-wrapper" role="article">
      <Link
        to={`/${post.slug}`}
        className="poem-card"
        aria-label={`Read post: ${post.title} by ${getAuthorName(post.author_id)}`}
      >
        {thumbnailUrl && (
          <div className="poem-image">
            <OptimizedImage
              src={thumbnailUrl}
              alt={`Image for ${post.title}`}
              width={400}
              height={150}
              lazy={true}
              style={{
                borderRadius: '8px',
                marginBottom: '10px'
              }}
            />
          </div>
        )}
        <header>
          <h3 className="poem-title">{post.title}</h3>
        </header>
        <div className="poem-preview" aria-label="Post excerpt">
          {getExcerpt(post.content)}
        </div>
        <footer className="poem-meta">
          <div className="author" aria-label="Author">
            By {getAuthorName(post.author_id)}
          </div>
          <time
            className="date"
            dateTime={post.published_at}
            aria-label="Published date"
          >
            {formatDate(post.published_at)}
          </time>
        </footer>
      </Link>
    </article>
  )
}

export default PostCard
