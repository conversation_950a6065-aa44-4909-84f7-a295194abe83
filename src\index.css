* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica', Arial, sans-serif;
  background: #fafafa;
  color: #333;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Header */
.header {
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  padding: 20px 30px;
  background: white;
  border: 1px solid #f0f0f0;
  margin-bottom: 20px;
}

.logo {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  text-decoration: none;
}

.nav {
  display: flex;
  gap: 30px;
  justify-content: center;
}

.nav-item {
  padding: 8px 0;
  border-bottom: 2px solid transparent;
  font-size: 14px;
  cursor: pointer;
  text-decoration: none;
  color: #333;
  transition: border-color 0.2s ease;
}

.nav-item:hover,
.nav-item.active {
  border-bottom-color: #333;
}

.search-container {
  position: relative;
}

.search {
  width: 200px;
  height: 35px;
  border: 1px solid #ddd;
  background: #f9f9f9;
  padding: 0 15px;
  font-size: 12px;
  border-radius: 4px;
}

.search:focus {
  outline: none;
  border-color: #333;
  background: white;
}

/* Main Grid */
.main-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin: 40px 0;
  /* Prevent layout shift during loading */
  min-height: 400px;
}

.poem-card {
  background: white;
  border: 1px solid #f0f0f0;
  padding: 30px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
  /* Prevent layout shift */
  min-height: 200px;
  contain: layout style;
}

.poem-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.poem-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
  line-height: 1.3;
}

.poem-preview {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.poem-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.author {
  font-weight: 500;
}

.date {
  font-size: 11px;
}

/* Featured Section */
.featured {
  grid-column: 1 / -1;
  background: white;
  border: 1px solid #f0f0f0;
  padding: 50px;
  text-align: center;
  margin-bottom: 20px;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.featured:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.featured-title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.featured-content {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.featured-author {
  font-size: 14px;
  color: #999;
  font-weight: 500;
}

/* Single Poem Layout */
.single-poem-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  margin: 40px 0;
}

.poem-content {
  background: white;
  border: 1px solid #f0f0f0;
  padding: 50px;
}

.poem-full-title {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 30px;
  color: #333;
  line-height: 1.2;
}

.poem-text {
  font-size: 16px;
  line-height: 1.8;
  color: #444;
  white-space: pre-line;
}

.poem-sidebar {
  display: grid;
  gap: 20px;
}

.sidebar-section {
  background: white;
  border: 1px solid #f0f0f0;
  padding: 25px;
}

.sidebar-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.sidebar-content {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* Author Grid */
.author-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 40px 0;
}

.author-card {
  background: white;
  border: 1px solid #f0f0f0;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  text-decoration: none;
  color: inherit;
}

.author-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.author-avatar {
  width: 60px;
  height: 60px;
  background: #ddd;
  border-radius: 50%;
  margin: 0 auto 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: #666;
}

.author-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

.author-count {
  font-size: 12px;
  color: #999;
}

/* Footer */
.footer {
  background: white;
  border: 1px solid #f0f0f0;
  padding: 40px 30px;
  margin-top: 50px;
  text-align: center;
  color: #666;
}

/* Loading and Error States */
.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}

.loading-message {
  margin-left: 10px;
  font-size: 14px;
}

.error {
  text-align: center;
  padding: 40px;
  color: #e74c3c;
}

/* Enhanced Error States */
.error-state {
  text-align: center;
  padding: 40px;
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin: 20px 0;
}

.error-content {
  max-width: 400px;
  margin: 0 auto;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.error-message {
  color: #666;
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 20px;
}

.retry-btn {
  background: #333;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.retry-btn:hover:not(:disabled) {
  background: #555;
  transform: translateY(-1px);
}

.retry-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.retry-btn.retrying {
  background: #999;
}

.spinner.small {
  width: 16px;
  height: 16px;
  border-width: 2px;
  margin-right: 0;
}

/* Loading Spinner */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #333;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
  vertical-align: middle;
}

/* Load More Button */
.load-more-container {
  text-align: center;
  margin: 30px 0;
}

.load-more-btn {
  padding: 12px 24px;
  background: #333;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.load-more-btn:hover:not(:disabled) {
  background: #555;
}

.load-more-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Improved loading states */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 10px;
}

.loading-inline {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

/* Image optimization and layout stability */
.optimized-image-container {
  position: relative;
  overflow: hidden;
}

.image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Prevent layout shift for images */
.poem-image, .featured-image {
  aspect-ratio: 16/9;
  overflow: hidden;
}

.poem-image img, .featured-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Page Transitions */
.page-transition {
  transition: opacity 300ms cubic-bezier(0.4, 0, 0.2, 1),
              transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition.fadeIn {
  opacity: 1;
  transform: translateY(0);
}

.page-transition.fadeOut {
  opacity: 0;
  transform: translateY(-10px);
}

/* Route-specific transitions */
.container {
  position: relative;
  overflow: hidden;
}

/* Smooth content loading */
.content-enter {
  opacity: 0;
  transform: translateY(20px);
}

.content-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 400ms ease-out, transform 400ms ease-out;
}

.content-exit {
  opacity: 1;
  transform: translateY(0);
}

.content-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 200ms ease-in, transform 200ms ease-in;
}

/* Lazy Image Loading */
.lazy-image-container {
  overflow: hidden;
  border-radius: 8px;
}

.lazy-image-skeleton {
  border-radius: 8px;
}

.lazy-image-error {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

/* Smooth hover transitions */
.poem-card,
.featured,
.author-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.poem-card:hover,
.featured:hover,
.author-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus management for accessibility */
.poem-card:focus,
.featured:focus,
.author-card:focus,
.nav-item:focus,
.search:focus,
.load-more-btn:focus,
.retry-btn:focus {
  outline: 2px solid #007cba;
  outline-offset: 2px;
}

/* Skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
  border-radius: 4px;
}

.skip-link:focus {
  top: 6px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .skeleton-shimmer {
    background: linear-gradient(90deg, #000 25%, #333 50%, #000 75%);
  }

  .error-state {
    border: 2px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .poem-card,
  .featured,
  .author-card,
  .page-transition-enter-active,
  .page-transition-exit-active {
    transition: none;
  }

  .poem-card:hover,
  .featured:hover,
  .author-card:hover {
    transform: none;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .poem-card:hover,
  .featured:hover,
  .author-card:hover {
    transform: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }

  .poem-card:active,
  .featured:active,
  .author-card:active {
    transform: scale(0.98);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    grid-template-columns: 1fr;
    gap: 20px;
    text-align: center;
  }

  .nav {
    order: 2;
  }

  .search {
    width: 100%;
    max-width: 300px;
  }

  .single-poem-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .poem-content,
  .sidebar-section {
    padding: 30px 20px;
  }

  .featured {
    padding: 30px 20px;
  }

  .featured-title {
    font-size: 24px;
  }

  .poem-full-title {
    font-size: 28px;
  }

  /* Mobile-specific touch optimizations */
  .poem-card,
  .featured,
  .author-card {
    padding: 20px;
  }

  .main-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .error-state {
    padding: 30px 20px;
    margin: 15px 0;
  }

  .error-icon {
    font-size: 36px;
    margin-bottom: 15px;
  }
}
